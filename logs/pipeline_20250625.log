2025-06-25 07:54:02,467 - pipeline.core.data_sync_manager - WARNING - _init_redis:71 - Red<PERSON> connection failed: <PERSON>rror 61 connecting to localhost:6379. Connection refused.
2025-06-25 07:54:02,468 - pipeline.core.data_sync_manager - INFO - sync_redis_from_csv:328 - Redis not available, skipping sync
2025-06-25 07:54:02,468 - __main__ - INFO - initialize:125 - 🚀 Initializing Enhanced MCP Pipeline System
2025-06-25 07:54:02,468 - __main__ - ERROR - initialize:142 - ❌ Failed to initialize pipeline system: 'StateManager' object has no attribute 'logger'
2025-06-25 07:54:02,468 - __main__ - INFO - cleanup:400 - 🧹 Cleaning up resources...
2025-06-25 07:54:02,468 - __main__ - INFO - cleanup:405 - ✅ Cleanup complete
2025-06-25 07:54:35,556 - pipeline.core.data_sync_manager - INFO - _init_redis:69 - Connected to Redis for data synchronization
2025-06-25 07:54:35,559 - pipeline.core.data_sync_manager - ERROR - sync_redis_from_csv:365 - Error syncing Redis from CSV: Invalid input of type: 'list'. Convert to a bytes, string, int or float first.
2025-06-25 07:54:35,559 - __main__ - INFO - initialize:125 - 🚀 Initializing Enhanced MCP Pipeline System
2025-06-25 07:54:35,560 - __main__ - ERROR - initialize:142 - ❌ Failed to initialize pipeline system: 'StateManager' object has no attribute 'logger'
2025-06-25 07:54:35,560 - __main__ - INFO - cleanup:400 - 🧹 Cleaning up resources...
2025-06-25 07:54:35,560 - __main__ - INFO - cleanup:405 - ✅ Cleanup complete
2025-06-25 07:55:14,315 - pipeline.core.data_sync_manager - INFO - _init_redis:69 - Connected to Redis for data synchronization
2025-06-25 07:55:14,321 - pipeline.core.data_sync_manager - ERROR - sync_redis_from_csv:365 - Error syncing Redis from CSV: Invalid input of type: 'list'. Convert to a bytes, string, int or float first.
2025-06-25 07:55:14,321 - __main__ - INFO - initialize:125 - 🚀 Initializing Enhanced MCP Pipeline System
2025-06-25 07:55:14,322 - __main__ - ERROR - initialize:142 - ❌ Failed to initialize pipeline system: 'StateManager' object has no attribute 'logger'
2025-06-25 07:55:14,323 - __main__ - INFO - cleanup:400 - 🧹 Cleaning up resources...
2025-06-25 07:55:14,323 - __main__ - INFO - cleanup:405 - ✅ Cleanup complete
2025-06-25 09:19:03,015 - pipeline.core.data_sync_manager - WARNING - _init_redis:71 - Redis connection failed: Error 61 connecting to localhost:6379. Connection refused.
2025-06-25 09:19:03,015 - pipeline.core.data_sync_manager - INFO - sync_redis_from_csv:328 - Redis not available, skipping sync
2025-06-25 09:19:03,015 - __main__ - INFO - initialize:125 - 🚀 Initializing Enhanced MCP Pipeline System
2025-06-25 09:19:03,015 - __main__ - ERROR - initialize:142 - ❌ Failed to initialize pipeline system: 'StateManager' object has no attribute 'logger'
2025-06-25 09:19:03,015 - __main__ - INFO - cleanup:400 - 🧹 Cleaning up resources...
2025-06-25 09:19:03,015 - __main__ - INFO - cleanup:405 - ✅ Cleanup complete
2025-06-25 09:22:12,884 - pipeline.core.data_sync_manager - WARNING - _init_redis:71 - Redis connection failed: Error 61 connecting to localhost:6379. Connection refused.
2025-06-25 09:22:12,884 - pipeline.core.data_sync_manager - INFO - sync_redis_from_csv:328 - Redis not available, skipping sync
2025-06-25 09:22:12,884 - __main__ - INFO - initialize:125 - 🚀 Initializing Enhanced MCP Pipeline System
2025-06-25 09:22:12,884 - __main__ - ERROR - initialize:142 - ❌ Failed to initialize pipeline system: 'StateManager' object has no attribute 'logger'
2025-06-25 09:22:12,884 - __main__ - INFO - cleanup:400 - 🧹 Cleaning up resources...
2025-06-25 09:22:12,884 - __main__ - INFO - cleanup:405 - ✅ Cleanup complete
