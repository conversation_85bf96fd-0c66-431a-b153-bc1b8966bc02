#!/usr/bin/env python3
"""
Enhanced State Management System
Redis-based state management with CSV fallback for pipeline tracking,
progress monitoring, and resumability features.
"""

import json
import time
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from pathlib import Path
from dataclasses import dataclass, asdict
from enum import Enum

# Redis imports with fallback
try:
    import redis
    import aioredis
    REDIS_AVAILABLE = True
except ImportError:
    REDIS_AVAILABLE = False
    logging.warning("Redis not available. Using file-based state management.")


@dataclass
class StateSnapshot:
    """State snapshot for checkpointing"""
    pipeline_id: str
    timestamp: datetime
    current_task_index: int
    completed_count: int
    failed_count: int
    total_count: int
    last_processed_url: str
    metadata: Dict[str, Any]

    def to_dict(self) -> Dict[str, Any]:
        data = asdict(self)
        data['timestamp'] = data['timestamp'].isoformat()
        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'StateSnapshot':
        data['timestamp'] = datetime.fromisoformat(data['timestamp'])
        return cls(**data)


class StateManager:
    """Enhanced state management with Redis and file fallback"""
    
    def __init__(self, redis_url: str = "redis://localhost:6379", 
                 redis_db: int = 1, data_dir: str = "data"):
        self.redis_url = redis_url
        self.redis_db = redis_db
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        
        self.redis_client = None
        self.async_redis_client = None
        
        # Initialize Redis connections
        self._init_redis()
        
        # State tracking
        self.active_pipelines = set()
        self.pipeline_states = {}
        
        self.logger = logging.getLogger(__name__)

    def _init_redis(self):
        """Initialize Redis connections"""
        if not REDIS_AVAILABLE:
            self.logger.warning("Redis not available, using file-based state management")
            return
        
        try:
            # Synchronous Redis client
            self.redis_client = redis.from_url(
                self.redis_url, 
                db=self.redis_db,
                decode_responses=True,
                socket_timeout=5,
                socket_connect_timeout=5
            )
            self.redis_client.ping()
            self.logger.info("Connected to Redis for state management")
            
        except Exception as e:
            self.logger.warning(f"Redis connection failed: {e}. Using file-based fallback.")
            self.redis_client = None

    async def _get_async_redis(self):
        """Get async Redis client"""
        if not REDIS_AVAILABLE or not self.redis_client:
            return None
        
        if self.async_redis_client is None:
            try:
                self.async_redis_client = aioredis.from_url(
                    self.redis_url,
                    db=self.redis_db,
                    decode_responses=True
                )
                await self.async_redis_client.ping()
                self.logger.debug("Async Redis client initialized")
            except Exception as e:
                self.logger.warning(f"Async Redis connection failed: {e}")
                self.async_redis_client = None
        
        return self.async_redis_client

    def _get_state_file_path(self, pipeline_id: str) -> Path:
        """Get state file path for pipeline"""
        return self.data_dir / f"state_{pipeline_id}.json"

    def _get_checkpoint_file_path(self, pipeline_id: str) -> Path:
        """Get checkpoint file path for pipeline"""
        return self.data_dir / f"checkpoint_{pipeline_id}.json"

    async def create_pipeline_state(self, pipeline_id: str, 
                                  total_tasks: int, 
                                  task_type: str = 'scrape',
                                  metadata: Dict[str, Any] = None) -> bool:
        """Create initial state for a new pipeline"""
        try:
            state = {
                'pipeline_id': pipeline_id,
                'task_type': task_type,
                'status': 'created',
                'total_tasks': total_tasks,
                'completed_tasks': 0,
                'failed_tasks': 0,
                'skipped_tasks': 0,
                'current_task_index': 0,
                'last_processed_url': None,
                'created_at': datetime.now().isoformat(),
                'updated_at': datetime.now().isoformat(),
                'started_at': None,
                'completed_at': None,
                'progress_percentage': 0.0,
                'estimated_completion': None,
                'metadata': metadata or {}
            }
            
            # Store in Redis
            redis_client = await self._get_async_redis()
            if redis_client:
                try:
                    await redis_client.hset(
                        f"pipeline_state:{pipeline_id}",
                        mapping=state
                    )
                    await redis_client.expire(f"pipeline_state:{pipeline_id}", 86400 * 7)  # 7 days
                    self.logger.debug(f"Created pipeline state in Redis: {pipeline_id}")
                except Exception as e:
                    self.logger.warning(f"Redis state creation failed: {e}")
            
            # Store in file as backup
            state_file = self._get_state_file_path(pipeline_id)
            with open(state_file, 'w', encoding='utf-8') as f:
                json.dump(state, f, indent=2, ensure_ascii=False)
            
            self.active_pipelines.add(pipeline_id)
            self.pipeline_states[pipeline_id] = state
            
            self.logger.info(f"Created state for pipeline {pipeline_id} with {total_tasks} tasks")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to create pipeline state: {e}")
            return False

    async def update_pipeline_state(self, pipeline_id: str, 
                                  updates: Dict[str, Any]) -> bool:
        """Update pipeline state"""
        try:
            # Add timestamp
            updates['updated_at'] = datetime.now().isoformat()
            
            # Update Redis
            redis_client = await self._get_async_redis()
            if redis_client:
                try:
                    await redis_client.hset(
                        f"pipeline_state:{pipeline_id}",
                        mapping=updates
                    )
                except Exception as e:
                    self.logger.warning(f"Redis state update failed: {e}")
            
            # Update local cache
            if pipeline_id in self.pipeline_states:
                self.pipeline_states[pipeline_id].update(updates)
            
            # Update file backup
            state = await self.get_pipeline_state(pipeline_id)
            if state:
                state_file = self._get_state_file_path(pipeline_id)
                with open(state_file, 'w', encoding='utf-8') as f:
                    json.dump(state, f, indent=2, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to update pipeline state: {e}")
            return False

    async def get_pipeline_state(self, pipeline_id: str) -> Optional[Dict[str, Any]]:
        """Get current pipeline state"""
        try:
            # Try Redis first
            redis_client = await self._get_async_redis()
            if redis_client:
                try:
                    state = await redis_client.hgetall(f"pipeline_state:{pipeline_id}")
                    if state:
                        self.logger.debug(f"Retrieved state from Redis: {pipeline_id}")
                        return state
                except Exception as e:
                    self.logger.warning(f"Redis state retrieval failed: {e}")
            
            # Try local cache
            if pipeline_id in self.pipeline_states:
                return self.pipeline_states[pipeline_id]
            
            # Fallback to file
            state_file = self._get_state_file_path(pipeline_id)
            if state_file.exists():
                with open(state_file, 'r', encoding='utf-8') as f:
                    state = json.load(f)
                    self.pipeline_states[pipeline_id] = state
                    return state
            
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to get pipeline state: {e}")
            return None

    async def create_checkpoint(self, pipeline_id: str, 
                              current_task_index: int,
                              last_processed_url: str,
                              metadata: Dict[str, Any] = None) -> bool:
        """Create a checkpoint for resumability"""
        try:
            state = await self.get_pipeline_state(pipeline_id)
            if not state:
                return False
            
            checkpoint = StateSnapshot(
                pipeline_id=pipeline_id,
                timestamp=datetime.now(),
                current_task_index=current_task_index,
                completed_count=int(state.get('completed_tasks', 0)),
                failed_count=int(state.get('failed_tasks', 0)),
                total_count=int(state.get('total_tasks', 0)),
                last_processed_url=last_processed_url,
                metadata=metadata or {}
            )
            
            checkpoint_data = checkpoint.to_dict()
            
            # Store in Redis
            redis_client = await self._get_async_redis()
            if redis_client:
                try:
                    await redis_client.set(
                        f"checkpoint:{pipeline_id}",
                        json.dumps(checkpoint_data),
                        ex=86400 * 7  # 7 days expiry
                    )
                except Exception as e:
                    self.logger.warning(f"Redis checkpoint failed: {e}")
            
            # Store in file
            checkpoint_file = self._get_checkpoint_file_path(pipeline_id)
            with open(checkpoint_file, 'w', encoding='utf-8') as f:
                json.dump(checkpoint_data, f, indent=2, ensure_ascii=False)
            
            self.logger.debug(f"Created checkpoint for {pipeline_id} at task {current_task_index}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to create checkpoint: {e}")
            return False

    async def get_latest_checkpoint(self, pipeline_id: str) -> Optional[StateSnapshot]:
        """Get the latest checkpoint for resuming"""
        try:
            # Try Redis first
            redis_client = await self._get_async_redis()
            if redis_client:
                try:
                    data = await redis_client.get(f"checkpoint:{pipeline_id}")
                    if data:
                        checkpoint_data = json.loads(data)
                        return StateSnapshot.from_dict(checkpoint_data)
                except Exception as e:
                    self.logger.warning(f"Redis checkpoint retrieval failed: {e}")
            
            # Fallback to file
            checkpoint_file = self._get_checkpoint_file_path(pipeline_id)
            if checkpoint_file.exists():
                with open(checkpoint_file, 'r', encoding='utf-8') as f:
                    checkpoint_data = json.load(f)
                    return StateSnapshot.from_dict(checkpoint_data)
            
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to get checkpoint: {e}")
            return None

    async def mark_pipeline_started(self, pipeline_id: str) -> bool:
        """Mark pipeline as started"""
        return await self.update_pipeline_state(pipeline_id, {
            'status': 'running',
            'started_at': datetime.now().isoformat()
        })

    async def mark_pipeline_completed(self, pipeline_id: str) -> bool:
        """Mark pipeline as completed"""
        return await self.update_pipeline_state(pipeline_id, {
            'status': 'completed',
            'completed_at': datetime.now().isoformat(),
            'progress_percentage': 100.0
        })

    async def mark_pipeline_failed(self, pipeline_id: str, error: str) -> bool:
        """Mark pipeline as failed"""
        return await self.update_pipeline_state(pipeline_id, {
            'status': 'failed',
            'failed_at': datetime.now().isoformat(),
            'error_message': error
        })

    async def update_progress(self, pipeline_id: str, 
                            completed_tasks: int,
                            failed_tasks: int = 0,
                            skipped_tasks: int = 0) -> bool:
        """Update task progress"""
        try:
            state = await self.get_pipeline_state(pipeline_id)
            if not state:
                return False
            
            total_tasks = int(state.get('total_tasks', 0))
            progress = 0.0
            if total_tasks > 0:
                progress = ((completed_tasks + failed_tasks + skipped_tasks) / total_tasks) * 100
            
            # Estimate completion time
            estimated_completion = None
            if progress > 0 and progress < 100:
                started_at = state.get('started_at')
                if started_at:
                    start_time = datetime.fromisoformat(started_at)
                    elapsed = datetime.now() - start_time
                    if elapsed.total_seconds() > 0:
                        estimated_total = elapsed.total_seconds() * (100 / progress)
                        estimated_completion = (start_time + timedelta(seconds=estimated_total)).isoformat()
            
            updates = {
                'completed_tasks': completed_tasks,
                'failed_tasks': failed_tasks,
                'skipped_tasks': skipped_tasks,
                'progress_percentage': round(progress, 2),
                'estimated_completion': estimated_completion
            }
            
            return await self.update_pipeline_state(pipeline_id, updates)
            
        except Exception as e:
            self.logger.error(f"Failed to update progress: {e}")
            return False

    async def get_all_pipeline_states(self) -> Dict[str, Dict[str, Any]]:
        """Get states of all active pipelines"""
        try:
            states = {}
            
            # Get from Redis if available
            redis_client = await self._get_async_redis()
            if redis_client:
                try:
                    keys = await redis_client.keys("pipeline_state:*")
                    for key in keys:
                        pipeline_id = key.split(":", 1)[1]
                        state = await redis_client.hgetall(key)
                        if state:
                            states[pipeline_id] = state
                except Exception as e:
                    self.logger.warning(f"Redis bulk retrieval failed: {e}")
            
            # Supplement with file-based states
            for state_file in self.data_dir.glob("state_*.json"):
                pipeline_id = state_file.stem.replace("state_", "")
                if pipeline_id not in states:
                    try:
                        with open(state_file, 'r', encoding='utf-8') as f:
                            states[pipeline_id] = json.load(f)
                    except Exception as e:
                        self.logger.warning(f"Failed to load state file {state_file}: {e}")
            
            return states
            
        except Exception as e:
            self.logger.error(f"Failed to get all pipeline states: {e}")
            return {}

    async def cleanup_completed_states(self, days_old: int = 7) -> int:
        """Clean up old completed pipeline states"""
        try:
            cutoff_date = datetime.now() - timedelta(days=days_old)
            cleaned_count = 0
            
            states = await self.get_all_pipeline_states()
            
            for pipeline_id, state in states.items():
                completed_at = state.get('completed_at')
                if completed_at:
                    try:
                        completed_date = datetime.fromisoformat(completed_at)
                        if completed_date < cutoff_date:
                            # Remove from Redis
                            redis_client = await self._get_async_redis()
                            if redis_client:
                                await redis_client.delete(f"pipeline_state:{pipeline_id}")
                                await redis_client.delete(f"checkpoint:{pipeline_id}")
                            
                            # Remove files
                            state_file = self._get_state_file_path(pipeline_id)
                            checkpoint_file = self._get_checkpoint_file_path(pipeline_id)
                            
                            if state_file.exists():
                                state_file.unlink()
                            if checkpoint_file.exists():
                                checkpoint_file.unlink()
                            
                            # Remove from local cache
                            self.active_pipelines.discard(pipeline_id)
                            self.pipeline_states.pop(pipeline_id, None)
                            
                            cleaned_count += 1
                            self.logger.debug(f"Cleaned up old state for pipeline {pipeline_id}")
                            
                    except Exception as e:
                        self.logger.warning(f"Failed to parse completion date for {pipeline_id}: {e}")
            
            if cleaned_count > 0:
                self.logger.info(f"Cleaned up {cleaned_count} old pipeline states")
            
            return cleaned_count
            
        except Exception as e:
            self.logger.error(f"Failed to cleanup old states: {e}")
            return 0

    async def close(self):
        """Close Redis connections"""
        if self.async_redis_client:
            await self.async_redis_client.close()
        if self.redis_client:
            self.redis_client.close()
